import { FileChangeType, FilePermission, FileStore } from '@/utils/graphEditor/data';
import {
  DeleteIcon,
  FileIcon,
  FolderIcon,
  MoveIcon,
  ReadOnlyFileIcon,
  RenameIcon,
  ShareIcon,
  SharedFolderIcon,
  TutorialIcon,
} from '@/pages/home/<USER>';
import { Input } from '@tigergraph/app-ui-lib/input';
import { TreeLabelInteractable } from 'baseui/tree-view';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useMutation } from 'react-query';
import React, { KeyboardEvent, MouseEvent, ReactNode, useEffect, useState, useCallback, useRef } from 'react';
import { BiSolidChevronRight } from 'react-icons/bi';
import { PLACEMENT, TRIGGER_TYPE } from 'baseui/popover';
import { ConfirmModal } from '@/pages/editor/file/ConfirmModal';
import { deleteFile, moveFile, renameFileReq } from '@/pages/editor/file/api';
import { useSaveFileMutation } from '@/pages/editor/file/hooks';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { showToast } from '@/components/styledToasterContainer';
import { StyledButton } from '@/pages/editor/file/styleObject';
import StatefulPopover from '@/pages/editor/StatefulPopover';
import { useTheme } from '@/contexts/themeContext';
import { MdAdd } from 'react-icons/md';
import { Button } from '@tigergraph/app-ui-lib/button';
import { BsThreeDots } from 'react-icons/bs';
import { getErrorMessage } from '@/utils/utils';
import { AxiosError } from 'axios';
import { FaInfoCircle, FaSave } from 'react-icons/fa';
import { draggable, dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import clsx from 'clsx';

interface FileAction {
  label: string;
  handleFn?: Function;
  disabled: boolean;
  hidden: boolean;
  icon: React.ReactElement;
  popoverContent?: () => ReactNode;
}

export interface FileItemProps {
  file: FileStore;
  parent: FileStore | null;
  allFiles: FileStore[];
  newFileId: string;
  setNewFileId: (fileId: string) => void;
  onSelectFile: (file: FileStore) => void;
  onChangeFile: (type: FileChangeType, payload: FileStore) => void;
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
  onOpenShareDrawer: (file: FileStore) => void;
  onToggleExpand: (fileId: string) => void;
  onFileMenuOpen?: (isOpen: boolean) => void;
}

function isChild(file: FileStore, parent: FileStore | null) {
  if (!parent) {
    return !file.parent_id;
  }

  return !!parent.files?.find((f) => f.id === file.id);
}

function isFileDroppable(target: FileStore | null, source: FileStore) {
  if (isChild(source, target)) {
    return false;
  }

  return !target || (target.is_folder && target.type === 'UserFile');
}

function isFileStore(data: any): data is FileStore {
  return 'id' in data && 'is_folder' in data;
}

export function FileItem({
  file,
  parent,
  allFiles,
  newFileId,
  setNewFileId,
  onSelectFile,
  onChangeFile,
  onCreateFile,
  onOpenShareDrawer,
  onToggleExpand,
  onFileMenuOpen,
}: FileItemProps) {
  const { currentFileId, setActiveFiles, unsavedFiles } = useEditorContext();

  const [, theme] = useStyletron();
  const { themeType } = useTheme();

  const [tempName, setTempName] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  useEffect(() => {
    if (newFileId === file.id) {
      setIsEditing(true);
    }
  }, [newFileId, file.id]);
  useEffect(() => {
    setTempName(file.name);
  }, [file.name, isEditing]);
  const exitEditing = () => {
    setIsEditing(false);
    setNewFileId('');
  };

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const isOwner = file.permission === FilePermission.Owner;
  const maxMilliSeconds = Math.pow(2, 31) - 1;

  const userFolders = allFiles.filter((file) => file.is_folder && file.type === 'UserFile');

  const isActive = file.id === currentFileId;

  const handleClickFile = () => {
    if (file.is_folder) {
      onToggleExpand(file.id);
    } else if (file.id !== currentFileId) {
      onSelectFile(file);
    }
  };

  const renameFileClient = useMutation('renameFile', renameFileReq, {
    onSuccess: () => {
      exitEditing();
      onChangeFile(FileChangeType.UPDATE, { id: file.id, name: tempName } as FileStore);
    },
    onError: (error: AxiosError) => {
      showToast({ kind: 'negative', message: getErrorMessage(error) });
      exitEditing();
    },
  });

  const handleRenameFile = () => {
    if (tempName === file.name) {
      exitEditing();
      return;
    }
    if (!tempName) {
      showToast({ kind: 'negative', message: 'File name cannot be empty' });
      exitEditing();
      return;
    }
    // check whether tempName is duplicate with other files in the same folder
    if (allFiles.find((f) => f.name === tempName && f.parent_id === file.parent_id && f.id !== file.id)) {
      showToast({ kind: 'negative', message: 'File name already exists' });
      exitEditing();
      return;
    }

    renameFileClient.mutate({
      fileId: file.id,
      name: tempName,
    });
  };

  const deleteFileClient = useMutation('deleteFile', deleteFile, {
    onSuccess: () => {
      onChangeFile(FileChangeType.DELETE, file);
    },
    onError: (error: AxiosError) => {
      showToast({ kind: 'negative', message: getErrorMessage(error) });
    },
  });

  const moveFileClient = useMutation('moveFile', moveFile, {
    onSuccess: (data) => {
      onChangeFile(FileChangeType.MOVE, { ...data.Result });
    },
  });
  const handleMoveFile = useCallback(
    (fromId: string, parentFileId: string | null) => {
      moveFileClient.mutate({ fileId: fromId, parentFileId });
    },
    [moveFileClient]
  );
  const showMoveFile = (file: FileStore) => {
    return !file.is_folder && (userFolders.length !== 0 || !!file.parent_id) && isOwner;
  };

  const { mutate: saveFile } = useSaveFileMutation();

  const unsaved = !file.is_folder && unsavedFiles.find((f) => f.id === file.id || f.file_id === file.id);
  const fileActions: FileAction[] = [
    {
      label: 'Save',
      handleFn: () => {
        let unsavedFile = unsavedFiles.find((f) => f.id === file.id || f.file_id === file.id)!;
        saveFile(
          { content: unsavedFile.content, fileId: unsavedFile.file_id || unsavedFile.id },
          {
            onSuccess(data) {
              const { updated_at, id, content } = data.Result!;
              onChangeFile(FileChangeType.UPDATE, {
                id,
                updated_at,
                content,
              } as FileStore);
            },
          }
        );
      },
      disabled: !unsaved,
      hidden: ![FilePermission.Edit, FilePermission.Owner].includes(file.permission),
      icon: <FaSave size={16} color={theme.colors['icon.primary']} />,
    },
    {
      label: 'Share',
      handleFn: () => onOpenShareDrawer(file),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <ShareIcon />,
    },
    {
      label: 'Rename',
      handleFn: () => setIsEditing(true),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <RenameIcon />,
    },
    {
      label: 'Delete',
      handleFn: () => setIsDeleteModalOpen(true),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <DeleteIcon />,
    },
    {
      label: 'File Info',
      disabled: false,
      hidden: !parent?.isShared,
      icon: <FaInfoCircle size={16} color={theme.colors['icon.primary']} />,
      popoverContent: () => <FileInfo file={file} />,
    },
  ];

  const folderActions: FileAction[] = [
    {
      label: 'Rename',
      handleFn: () => setIsEditing(true),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <RenameIcon />,
    },
    {
      label: 'Delete',
      handleFn: () => setIsDeleteModalOpen(true),
      disabled: !isOwner,
      hidden: !isOwner,
      icon: <DeleteIcon />,
    },
  ];

  const displayActions = (file.is_folder ? folderActions : fileActions).filter((action) => !action.hidden);

  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!file.is_folder && file.type === 'UserFile') {
      const treeItem = ref.current!.closest('[role="treeitem"]') as HTMLElement;
      if (!treeItem) {
        return;
      }

      return draggable({
        element: treeItem,
        getInitialData: () => ({ ...file }),
      });
    }
  }, [file]);

  useEffect(() => {
    function makeElemDroppable(element: HTMLElement, file: FileStore | null) {
      return dropTargetForElements({
        element,
        canDrop: ({ source }) => {
          return isFileStore(source.data);
        },
        onDragEnter: ({ self, location, source }) => {
          if (location.current.dropTargets[0]?.element !== self.element) {
            return;
          }

          const data = source.data;
          if (!isFileStore(data)) {
            return;
          }

          if (isFileDroppable(file, data)) {
            element.style.backgroundColor = theme.colors['background.accent.neutral.subtler'];
          }
          // deactive other drop target
          const target = location.current.dropTargets.find((t) => t.element !== self.element);
          if (target) {
            (target.element as HTMLElement).style.backgroundColor = '';
          }
        },
        onDragLeave: ({ self, location, source }) => {
          element.style.backgroundColor = '';

          // if there is other drop target, active it
          const target = location.current.dropTargets.find((t) => t.element !== self.element);
          if (target && target.element.getAttribute('role') === 'tree') {
            (target.element as HTMLElement).style.backgroundColor = theme.colors['background.accent.neutral.subtler'];
          }
        },
        onDrop: ({ self, location, source }) => {
          if (location.current.dropTargets[0]?.element !== self.element) {
            return;
          }

          element.style.backgroundColor = '';

          const data = source.data;
          if (!isFileStore(data)) {
            return;
          }

          if (!isFileDroppable(file, data)) {
            return;
          }

          handleMoveFile(data.id, file ? file.id : null);
        },
      });
    }

    // make every user folder droppable
    if (file.is_folder) {
      const treeItem = ref.current!.closest('[role="treeitem"]') as HTMLElement;
      if (treeItem && !treeItem.getAttribute('data-drop-target-for-element')) {
        makeElemDroppable(treeItem, file);
      }
    }

    // make the outside of the tree droppable
    // this will be triggered once since there is only one Shared folder
    if (file.is_folder && file.isShared) {
      const treeRoot = ref.current!.closest('[role="tree"]') as HTMLElement;
      if (treeRoot && !treeRoot.getAttribute('data-drop-target-for-element')) {
        makeElemDroppable(treeRoot, null);
      }
    }
  }, [file, handleMoveFile, theme.colors]);

  return (
    <TreeLabelInteractable>
      <div
        data-baseweb={'tree-node-label'}
        ref={ref}
        className={clsx(
          'flex items-center justify-between text-primary',
          file.is_folder ? 'cursor-default' : 'cursor-pointer'
        )}
        style={{ color: theme.colors['text.primary'] }}
        onClick={handleClickFile}
      >
        <div
          className="flex items-center"
          style={{
            flexGrow: 2,
            width: `calc(100% - ${file.is_folder ? 40 : 24}px)`,
          }}
        >
          <div className="mr-1">
            {file.is_folder ? (
              file.isShared ? (
                <SharedFolderIcon />
              ) : file.type === 'Tutorial' ? (
                <TutorialIcon />
              ) : (
                <FolderIcon />
              )
            ) : file.permission === FilePermission.View ? (
              <ReadOnlyFileIcon />
            ) : (
              <FileIcon />
            )}
          </div>
          <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap">
            {isEditing ? (
              <Input
                value={tempName}
                autoFocus
                onChange={(e) => {
                  const { currentTarget } = e;
                  setTempName(() => currentTarget.value);
                }}
                onKeyDown={(e: KeyboardEvent) => {
                  switch (e.key) {
                    case 'Enter':
                      handleRenameFile();
                      return;
                  }
                }}
                onBlur={handleRenameFile}
                overrides={{
                  Root: {
                    style: {
                      borderTopWidth: '1px',
                      borderRightWidth: '1px',
                      borderBottomWidth: '1px',
                      borderLeftWidth: '1px',
                      height: '24px',
                    },
                  },
                  Input: {
                    style: {
                      fontSize: '12px',
                      paddingLeft: '4px',
                      paddingTop: 0,
                      paddingBottom: 0,
                    },
                  },
                }}
              />
            ) : (
              <div className="flex items-center" style={{ lineHeight: '16px' }}>
                <span className="relative overflow-hidden text-ellipsis whitespace-nowrap">{file.name}</span>
                {unsaved && <span>*</span>}
              </div>
            )}
          </div>
        </div>
        <div className={clsx('h-5 flex', 'file-actions')}>
          {file.is_folder && [FilePermission.Edit, FilePermission.Owner].includes(file.permission) && (
            <Button
              size="compact"
              kind="text"
              shape="square"
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event.stopPropagation();
                onCreateFile({ is_folder: false, content: '', parentId: file.id });
              }}
            >
              <MdAdd size={20} />
            </Button>
          )}
          {displayActions.length > 0 && (
            <StatefulPopover
              onOpen={() => {
                onFileMenuOpen?.(true);
              }}
              onClose={() => {
                onFileMenuOpen?.(false);
              }}
              // @ts-ignore
              onClick={(e) => {
                e.stopPropagation();
              }}
              ignoreBoundary
              animateOutTime={100}
              showArrow={false}
              placement={'rightTop'}
              overrides={{
                Body: {
                  style: ({ $theme }) => ({
                    width: '102px',
                    zIndex: 1,
                  }),
                },
                Inner: {
                  style: ({ $theme }) => ({
                    paddingTop: '8px',
                    paddingBottom: '8px',
                    paddingLeft: '0px',
                    paddingRight: '0px',
                  }),
                },
              }}
              content={({ close }) => (
                <>
                  {displayActions.map((action) => (
                    <FileItemAction key={action.label} action={action} close={close} />
                  ))}
                  {showMoveFile(file) && (
                    <StyledButton
                      disabled={!isOwner}
                      onClick={(e: MouseEvent<HTMLButtonElement>) => {
                        e.stopPropagation();
                      }}
                    >
                      <StatefulPopover
                        showArrow={false}
                        placement={isOwner ? PLACEMENT.rightTop : PLACEMENT.right}
                        triggerType={TRIGGER_TYPE.hover}
                        overrides={
                          isOwner
                            ? {
                                Inner: {
                                  style: {
                                    paddingTop: '0px',
                                    paddingRight: '0px',
                                    paddingLeft: '0px',
                                    paddingBottom: '0px',
                                    color: theme.colors['dropdown.text'],
                                  },
                                },
                              }
                            : { Inner: { style: { color: theme.colors['dropdown.text'] } } }
                        }
                        onMouseLeaveDelay={isOwner ? maxMilliSeconds : 200}
                        content={() => {
                          return isOwner ? (
                            <div>
                              {userFolders.length > 0 && (
                                <>
                                  <div
                                    className="text-xs font-bold py-2 px-2"
                                    style={{ color: theme.colors['dropdown.text'] }}
                                  >
                                    Folders
                                  </div>
                                  {userFolders.map((folder) => {
                                    return (
                                      <div
                                        key={folder.id}
                                        className="text-xs cursor-pointer flex px-2 hover:bg-opacity-80"
                                        style={{
                                          height: '30px',
                                          backgroundColor: theme.colors['dropdown.background'],
                                        }}
                                        onMouseEnter={(e) => {
                                          e.currentTarget.style.backgroundColor =
                                            theme.colors['dropdown.background.hover'];
                                        }}
                                        onMouseLeave={(e) => {
                                          e.currentTarget.style.backgroundColor = theme.colors['dropdown.background'];
                                        }}
                                        onClick={() => {
                                          handleMoveFile(file.id, folder.id);
                                          close();
                                        }}
                                      >
                                        <div className="flex items-center gap-3">
                                          <FolderIcon /> {folder.name}
                                        </div>
                                      </div>
                                    );
                                  })}
                                </>
                              )}
                              {file.parent_id && (
                                <div
                                  onClick={() => {
                                    handleMoveFile(file.id, null);
                                  }}
                                  className="text-xs px-2 py-2 cursor-pointer"
                                  style={{
                                    borderTop: `1px solid ${theme.colors['dropdown.border']}`,
                                    backgroundColor: theme.colors['dropdown.background'],
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.backgroundColor = theme.colors['dropdown.background.hover'];
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.backgroundColor = theme.colors['dropdown.background'];
                                  }}
                                >
                                  Remove from folder
                                </div>
                              )}
                            </div>
                          ) : (
                            'You are not the owner of this file'
                          );
                        }}
                      >
                        <div className="text-sm h-8 flex items-center">
                          <span className={clsx(!isOwner && 'opacity-50')}>
                            <MoveIcon />
                          </span>
                          <span className="ml-2">Move</span>
                          <BiSolidChevronRight />
                        </div>
                      </StatefulPopover>
                    </StyledButton>
                  )}
                </>
              )}
            >
              <Button size="compact" kind="text" shape="square">
                <BsThreeDots title="actions" size={20} color={theme.colors['icon.primary']} />
              </Button>
            </StatefulPopover>
          )}
        </div>
      </div>
      <ConfirmModal
        isFolder={file.is_folder}
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={() => deleteFileClient.mutate(file.id)}
      />
    </TreeLabelInteractable>
  );
}

function FileItemAction({ action, close }: { action: FileAction; close: () => void }) {
  const [, theme] = useStyletron();

  return (
    <StyledButton
      key={action.label}
      disabled={action.disabled}
      onClick={(e: MouseEvent<HTMLButtonElement>) => {
        if (action.handleFn) {
          action.handleFn();
          close();
        }
        e.stopPropagation();
      }}
    >
      <div className={clsx('text-sm h-8 flex items-center relative', action.disabled && 'opacity-50')}>
        <span>{action.icon}</span>
        <span
          className="ml-2"
          style={{
            color: action.label === 'Delete' ? theme.colors['dropdown.text.error'] : 'inherit',
          }}
        >
          {action.label}
        </span>
        {action.popoverContent ? (
          <StatefulPopover
            content={action.popoverContent}
            placement={PLACEMENT.rightTop}
            autoFocus={false}
            showArrow={false}
          >
            <div className="absolute inset-0" />
          </StatefulPopover>
        ) : null}
      </div>
    </StyledButton>
  );
}

function FileInfo({ file }: { file: FileStore }) {
  const [, theme] = useStyletron();
  const role =
    file.permission === FilePermission.Owner ? 'Owner' : file.permission === FilePermission.Edit ? 'Editor' : 'Viewer';

  const info = [
    { label: 'Owner', value: file.owner_email },
    { label: 'Your Role', value: role },
    { label: 'Created At', value: new Date(file.created_at).toLocaleString() },
    { label: 'Updated At', value: new Date(file.updated_at).toLocaleString() },
  ];

  return (
    <div>
      {info.map((item, index) => (
        <div className={clsx(index !== info.length - 1 && 'mb-2')} key={item.label}>
          <div
            style={{
              color: theme.colors['text.secondary'],
              ...theme.typography.Body2,
            }}
          >
            {item.label}:
          </div>
          <div
            style={{
              color: theme.colors['text.primary'],
              ...theme.typography.Body2,
            }}
          >
            {item.value}
          </div>
        </div>
      ))}
    </div>
  );
}
